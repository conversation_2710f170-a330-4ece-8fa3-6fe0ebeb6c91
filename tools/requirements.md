# Deployment Script Requirements

This document describes the functional and non-functional requirements for a Node.js-based deployment script to automate building and uploading the game to Steam.

## Overview

The deployment script will automate the following tasks:
- Bump the game version (major, minor, patch).
- Switch the application environment (production/demo).
- Build the project for all target platforms (Windows, Linux, macOS).
- Upload the builds to Steam using the Steam command-line tools.
- Manage Steam depot configuration (depot IDs per platform).

## Prerequisites

- Node.js >= 12.x installed and available on `PATH`.
- Godot Engine CLI (headless or editor) installed and available on `PATH`.
- SteamCMD or Steamworks SDK CLI installed and available on `PATH`.
- Deployment configuration file (`deploy.config.json`) at the repo root with Steam AppID and depot IDs.
- Export presets configured in `export_presets.cfg` for all target platforms.

## Configuration

### deploy.config.json

The script reads `deploy.config.json` at the project root to get Steam credentials and depot IDs:

```json
{
  "appId": 123456,
  "depots": {
    "windows": 111111,
    "linux":   222222,
    "mac":     333333
  }
}
```

### Environment Files

Environment-specific variables are defined in Godot script files under the `envs/` directory:

```
envs/
├── Environment.prod.steam.gd
├── Environment.prod.steam.demo.gd
...
```

The script will copy the chosen file to `app/Environment.gd` (which should be added to `.gitignore`) before building.

## Command-line Interface

The deployment script provides the following switches:

| Switch                          | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
| `--bump-version <major|minor|patch>` | Bump the version segment in `Config.gd` or `version.json`.  |
| `--checkout-env <prod|demo>`    | Copy the corresponding environment file to `Environment.gd`.    |
| `--build-all`                   | Export builds for Windows, Linux, and macOS.                   |
| `--upload`                      | Upload the built files to Steam using the Steam CLI.           |

### Example Usage

```bash
# Bump a patch version and switch to production environment
node deploy.js --bump-version patch --checkout-env prod

# Build all targets and upload to Steam
node deploy.js --build-all --upload
```

## Workflow

A typical deployment flow consists of:

1. Bump version:
   ```bash
   node deploy.js --bump-version patch
   ```
2. Switch environment:
   ```bash
   node deploy.js --checkout-env prod
   ```
3. Build all targets:
   ```bash
   node deploy.js --build-all
   ```
4. Upload to Steam:
   ```bash
   node deploy.js --upload
   ```

Alternatively, combine steps:

```bash
node deploy.js --bump-version patch --checkout-env prod --build-all --upload
```

## Detailed Requirements

### Version Bumping

- Read the current SemVer version from `app/Config.gd` or a centralized `version.json`.
- Increment the specified segment (`major`, `minor`, or `patch`).
- Update the version in all relevant files.

### Environment Checkout

- Copy `envs/Environment.prod.steam.gd` or `envs/Environment.prod.steam.demo.gd` to `app/Environment.gd`.
- Ensure `app/Environment.gd` is in `.gitignore`.

### Build All

- Use Godot CLI to export presets defined in `export_presets.cfg`:
  - Windows executable or archive.
  - Linux executable or archive.
  - macOS archive (extract after build and remove the original archive).
- Store outputs in a timestamped directory (e.g., `builds/YYYY-MM-DD-HHMMSS/`).

### Steam Upload

- Invoke SteamCMD or Steamworks SDK CLI with a VDF or direct depot upload commands.
- Use depot IDs from `deploy.config.json` for each platform.
- Provide clear console output indicating success or failure for each depot.

## Non-Functional Requirements

- Fail early on errors, with human-readable messages.
- Be idempotent and safe to run repeatedly.
- Support combining multiple switches in one command invocation.
- Cross-platform compatibility via Node.js and external CLI tools.