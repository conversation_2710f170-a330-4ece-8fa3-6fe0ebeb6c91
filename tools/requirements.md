# Deployment Script Requirements

This document describes the functional and non-functional requirements for a Node.js-based deployment script to automate building and uploading the game to Steam.

## Overview

The deployment script will automate the following tasks:
- Bump the game version (major, minor, patch).
- Switch the application environment (production/demo).
- Build the project for all target platforms (Windows, Linux, macOS).
- Upload the builds to Steam using the Steam command-line tools.
- Manage Steam depot configuration (depot IDs per platform).

## Prerequisites

- Node.js >= 12.x installed and available on `PATH`.
- Godot Engine CLI (headless or editor) installed and available on `PATH`.
- SteamCMD or Steamworks SDK CLI installed and available on `PATH`.
- Deployment configuration file (`deploy.config.json`) at the repo root with Steam AppID and depot IDs.
- Export presets configured in `export_presets.cfg` for all target platforms.

## Configuration

### deploy.config.json

The script reads `deploy.config.json` at the project root to get Steam credentials and depot IDs:

```json
{
  "appId": 123456,
  "depots": {
    "prod": {
      "windows": 3391212,
      "linux":   3391211,
      "mac":     3391213
    },
    "demo": {
      "windows": 3612922,
      "linux":   3612921,
      "mac":     3612923
    }
  }
}
```

### Environment Files

Environment-specific variables are defined in Godot script files under the `envs/` directory:

```
envs/
├── Environment.prod.steam.gd
├── Environment.prod.steam.demo.gd
...
```

The script will copy the chosen file to `app/Environment.gd` (which should be added to `.gitignore`) before building.

## Command-line Interface

The deployment script provides the following switches:

| Switch                          | Description                                                    |
|---------------------------------|----------------------------------------------------------------|
| `--bump-version <major|minor|patch>` | Bump the version segment in `Config.gd`.                     |
| `--checkout-env <prod|demo>`    | Copy the corresponding environment file to `Environment.gd`.    |
| `--build-all`                   | Export builds for Windows, Linux, and macOS.                   |
| `--build-windows`               | Export build for Windows only.                                 |
| `--build-linux`                | Export build for Linux only.                                   |
| `--build-mac`                   | Export build for macOS only.                                   |
| `--upload`                      | Upload the built files to Steam using the Steam CLI.           |
| `--content-desc <description>`  | Add release notes/content description for Steam upload.        |

### Example Usage

```bash
# Bump a patch version and switch to production environment
node deploy.js --bump-version patch --checkout-env prod

# Build all targets and upload to Steam with release notes
node deploy.js --build-all --upload --content-desc "Bug fixes and performance improvements"

# Build only Windows version
node deploy.js --build-windows

# Full deployment workflow
node deploy.js --bump-version patch --checkout-env prod --build-all --upload --content-desc "Version 1.2.3 release"
```

## Workflow

A typical deployment flow consists of:

1. Bump version:
   ```bash
   node deploy.js --bump-version patch
   ```
2. Switch environment:
   ```bash
   node deploy.js --checkout-env prod
   ```
3. Build all targets:
   ```bash
   node deploy.js --build-all
   ```
4. Upload to Steam:
   ```bash
   node deploy.js --upload
   ```

Alternatively, combine steps:

```bash
node deploy.js --bump-version patch --checkout-env prod --build-all --upload
```

## Detailed Requirements

### Version Bumping

- Read the current SemVer version from `app/Config.gd` (primary source).
- Increment the specified segment (`major`, `minor`, or `patch`).
- Update the version in `app/Config.gd`.
- For platforms requiring 4-number versioning (Windows/macOS), use format `major.minor.patch.0`.

### Environment Checkout

- Copy `envs/Environment.prod.steam.gd` or `envs/Environment.prod.steam.demo.gd` to `app/Environment.gd`.
- Ensure `app/Environment.gd` is in `.gitignore`.

### Build All / Platform-Specific Builds

- Use Godot CLI to export presets defined in `export_presets.cfg`:
  - Windows executable or archive.
  - Linux executable or archive.
  - macOS archive (extract after build and remove the original archive).
- Clean target directories before building to ensure fresh builds.
- Store outputs in the `_release/[os]/current` directory structure:
  - Windows builds: `_release/windows/current/`
  - Linux builds: `_release/linux/current/`
  - macOS builds: `_release/mac/current/`
- Support individual platform builds via `--build-windows`, `--build-linux`, `--build-mac` flags.
- Update application build version in Godot export settings if supported by CLI (investigate Godot CLI capabilities for version injection).

### Steam Upload

- Invoke SteamCMD or Steamworks SDK CLI with a VDF or direct depot upload commands.
- Use depot IDs from `deploy.config.json` based on the selected environment (prod/demo).
- Support optional content description via `--content-desc` flag for release notes.
- Provide clear console output indicating success or failure for each depot.

## Implementation Notes

### Build Version Injection

- Investigate Godot CLI capabilities for injecting version numbers during export.
- If Godot CLI supports version injection, update the application build version during export.
- For platforms requiring 4-number versioning, use `major.minor.patch.0` format.
- If CLI version injection is not available, document this limitation and suggest manual export preset configuration.

## Non-Functional Requirements

- Fail early on errors, with human-readable messages.
- Be idempotent and safe to run repeatedly.
- Support combining multiple switches in one command invocation.
- Cross-platform compatibility via Node.js and external CLI tools.
- Generate detailed logs to `deploy.log` for debugging and audit purposes.
- Keep the script simple and focused on core deployment tasks.