<!DOCTYPE html>
<html>
<head>
    <title>Canvas Grid Application</title>
    <style>
      #myCanvas {
          background-color: #FFFFDD;
      }
      #jsonOutput {
        width: 600px;
        height: 200px;
      }
    </style>
</head>
<body>
  <h1>Tryu Enemy Pattern Editor</h1>
    <canvas id="myCanvas" width="600" height="450" style="border:1px solid #000000;"></canvas>
    <br/>
    <br/>
    <button id="undo">Undo</button>
    <button id="clear">Clear</button>
    <button id="save">Save</button>
    <button id="load">Load</button>
    <br/>
    <br/>
    <textarea id="jsonOutput" rows="5" cols="50"></textarea>
    <script>
document.addEventListener('DOMContentLoaded', function () {
    const canvas = document.getElementById('myCanvas');
    const ctx = canvas.getContext('2d');
    const gridWidth = canvas.width / 12;
    const gridHeight = canvas.height / 8;
    let currentLine = [];

    // Draw initial grid and blue rectangle
    function drawGrid() {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = 'white';
        ctx.fillRect(gridWidth, gridHeight, canvas.width - 2 * gridWidth, canvas.height - 2 * gridHeight);

        for (let x = 0; x <= canvas.width; x += gridWidth) {
            for (let y = 0; y <= canvas.height; y += gridHeight) {
                ctx.strokeStyle = 'lightgrey';
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }
        }

        drawCurrentLine();

        currentLine.forEach(point => {
            drawPoint(point, 'gray');
        });

    }

    function drawPoint(point, color) {
        ctx.fillStyle = color;
        ctx.beginPath();
        ctx.arc(point.x, point.y, 5, 0, 2 * Math.PI);
        ctx.fill();
    }

    function drawCurrentLine() {
        if (currentLine.length < 2) return;
        ctx.strokeStyle = 'red';
        ctx.beginPath();
        ctx.moveTo(currentLine[0].x, currentLine[0].y);
        for (let i = 1; i < currentLine.length; i++) {
            ctx.lineTo(currentLine[i].x, currentLine[i].y);
        }
        ctx.stroke();
    }

    // Convert canvas coordinates to grid coordinates
    function toGridCoords(x, y) {
        return {
            x: Math.round(x / gridWidth) * gridWidth,
            y: Math.round(y / gridHeight) * gridHeight
        };
    }

    canvas.addEventListener('mousemove', function (e) {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const gridCoords = toGridCoords(x, y);

        drawGrid();
        drawPoint(gridCoords, 'red');
    });

    canvas.addEventListener('click', function (e) {
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const gridCoords = toGridCoords(x, y);

        currentLine.push(gridCoords);
      /*
        if (currentLine.find(p => p.x === gridCoords.x && p.y === gridCoords.y)) {
            currentLine = currentLine.filter(p => p.x !== gridCoords.x || p.y !== gridCoords.y);
        } else {
            currentLine.push(gridCoords);
        }
        */

        drawGrid();
    });

    document.getElementById('undo').addEventListener('click', function () {
        currentLine.pop();
        drawGrid();
    });

    document.getElementById('clear').addEventListener('click', function () {
        if (confirm('Clear all points?')) {
            currentLine = [];
            drawGrid();
        }
    });

    document.getElementById('save').addEventListener('click', function () {
        const dots = currentLine.map(p => [Math.round(p.x / gridWidth) - 1, Math.round(p.y / gridHeight) - 1]);
        document.getElementById('jsonOutput').value = JSON.stringify({ duration: 5.0, sharpness: 0.0, offsetx: 0, offsety: 0, mirrorx: 0, mirrory: 0, points: dots });
    });

    document.getElementById('load').addEventListener('click', function () {
        const input = document.getElementById('jsonOutput').value;
        try {
            const data = JSON.parse(input);
            if (!data.points || !Array.isArray(data.points)) throw new Error('Invalid format');
            currentLine = data.points.map(dot => ({
                x: (dot[0] + 1) * gridWidth,
                y: (dot[1] + 1) * gridHeight
            }));
            drawGrid();
        } catch (e) {
            alert('Invalid JSON: ' + e.message);
        }
    });

    drawGrid();
});
    </script>
</body>
</html>
