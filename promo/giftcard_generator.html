<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tryü Sora Gift Cards</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Space+Grotesk:wght@300..700&display=swap');

        body {
            font-family: 'Space Grotesk', sans-serif;
            background-color: #fff;
            color: #000;
            margin: 0;
            padding: 0;
        }

        .page {
            width: 210mm;
            height: 297mm;
            padding: 10mm;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 5mm;
            box-sizing: border-box;
            page-break-after: always;
        }

        .card {
            width: 92mm;
            height: 52mm;
            background: linear-gradient(135deg, #ddd, #fff);
            color: #000;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            padding: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            text-align: center;
            border: 2px solid #000;
            box-sizing: border-box;
        }

        .title {
            font-size: 16px;
            font-weight: 700;
            text-transform: uppercase;
            color: #000;
        }

        .subtitle {
            font-size: 11px;
            font-weight: 400;
            color: #333;
        }

        .key {
            font-family: 'Press Start 2P', sans-serif;        
            font-size: 14px;
            font-weight: 200;
            padding: 5px 10px;
            background-color: #eee;
            border: 2px dashed #000;
            border-radius: 5px;
            color: #000;
            margin: 5px 0;
        }

        .promo {
            font-size: 11px;
            color: #444;
        }

        .footer {
            font-size: 11px;
            font-weight: 600;
            color: #000;
        }

        @media print {
            body {
                background: none;
            }
            .page {
                box-shadow: none;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div id="giftCards"></div>

    <script>
        const STEAM_KEY = `XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX
XXXXX-XXXXX-XXXXX`; // Replace with actual keys

        function generateGiftCards() {
            const keys = STEAM_KEY.split("\n").map(k => k.trim()).filter(k => k.length > 0);
            const giftCardsContainer = document.getElementById("giftCards");

            let page = document.createElement("div");
            page.className = "page";

            keys.forEach((key, index) => {
                if (index > 0 && index % 10 === 0) {
                    giftCardsContainer.appendChild(page);
                    page = document.createElement("div");
                    page.className = "page";
                }

                const card = document.createElement("div");
                card.className = "card";
                card.innerHTML = `
                    <div class="title">Tryü Sora - STEAM GIFT CARD</div>
                    <div class="subtitle">Blast through space in this pixel-perfect arcade shooter!</div>
                    <div class="key">${key}</div>
                    <div class="promo">Dodge, shoot, upgrade, and survive! Uncover secrets, defeat bosses, and claim the leaderboard!</div>
                    <div class="footer">Download on Steam and start your journey!</div>
                `;
                page.appendChild(card);
            });

            giftCardsContainer.appendChild(page);
        }

        generateGiftCards();
    </script>
</body>
</html>
