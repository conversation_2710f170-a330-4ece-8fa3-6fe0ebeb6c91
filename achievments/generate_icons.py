import os
import sys
import argparse
import openai
import requests

# Parse command line arguments
parser = argparse.ArgumentParser(description='Generate achievement icons using OpenAI API.')
parser.add_argument('api_key', help='Your OpenAI API key')
args = parser.parse_args()

# Set your OpenAI API key
openai.api_key = args.api_key

# Read achievements from file
achievements = []
with open('achievements.txt', 'r', encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if line and not line.startswith('#'):
            parts = line.split('|')
            if len(parts) >= 4:
                achievement = {
                    'id': parts[0].strip(),
                    'name': parts[1].strip(),
                    'description': parts[2].strip(),
                    'secret': parts[3].strip().lower() == 'true',
                    'prompt': parts[4].strip()
                }
                achievements.append(achievement)

# Ensure the output directory exists
output_dir = 'achievement_icons'
os.makedirs(output_dir, exist_ok=True)

# Function to generate and save the image
def generate_achievement_icon(achievement):
    prompt = (
        f"{achievement['prompt']}"
        "MATERIAL APPLICATION ICON."
        "NO BORDER."
        "SIMPLE DESIGN."
        "STYLIZED."
        "SPACE AND STARS IN THE BACKGROUND."
        "BLACK BACKGROUND."
    )
    print(f"{prompt}")

    try:
        response = openai.Image.create(
            prompt=prompt,
            n=1,
            size='256x256'
        )
        image_url = response['data'][0]['url']

        # Download the image
        image_data = requests.get(image_url).content
        image_path = os.path.join(output_dir, f"{achievement['id']}.png")
        with open(image_path, "wb") as f:
            f.write(image_data)
        print(f"Icon for '{achievement['name']}' saved as '{image_path}'.")
    except Exception as e:
        print(f"Failed to generate icon for '{achievement['name']}': {e}")

# Generate icons for all achievements
for achievement in achievements:
    generate_achievement_icon(achievement)
