#!/bin/bash

# Usage: ./run_generate_icons.sh YOUR_OPENAI_API_KEY

API_KEY=$1

if [ -z "$API_KEY" ]; then
    echo "Usage: $0 YOUR_OPENAI_API_KEY"
    exit 1
fi

# Check if virtual environment directory exists
if [ ! -d "venv" ]; then
    echo "Virtual environment not found. Creating one..."
    python3 -m venv venv
    source venv/bin/activate
    echo "Installing required packages..."
    pip install -r requirements.txt
else
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

echo "Running the script..."
python generate_icons.py "$API_KEY"

echo "Deactivating virtual environment..."
deactivate
