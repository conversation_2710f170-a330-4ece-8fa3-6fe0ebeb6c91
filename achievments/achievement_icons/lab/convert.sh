#!/bin/bash

# Check if ImageMagick is installed
if ! command -v magick convert &> /dev/null; then
    echo "ImageMagick is not installed. Please install it using your package manager."
    echo "For example: sudo apt-get install imagemagick"
    exit 1
fi

# Process all PNG files in the current directory
shopt -s nullglob
for img in *.png; do
    if [ -f "$img" ]; then
        echo "Processing '$img'..."

        # Pixelate the image
        magick convert "$img" -scale 20% -scale 500% "${img%.png}_pixelated.png"

        # color palette convertion
        magick convert -size 1x256 gradient:black-green ___palette.png
        magick convert "${img%.png}_pixelated.png" -colorspace Gray +sigmoidal-contrast 6,50% ___palette.png -clut "${img%.png}_green.png"

        # Add a 5-pixel black border
        magick convert "${img%.png}_green.png" -bordercolor black -border 10 "_${img%.png}.png"

        # Clean up the intermediate file
        rm "${img%.png}_pixelated.png"
        rm "${img%.png}_green.png"

        echo "'$img' processed and saved as '_${img%.png}.png'."
    fi
done
