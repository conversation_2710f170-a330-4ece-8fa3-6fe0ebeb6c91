#!/bin/bash

# Ensure the input file exists
if [ ! -f "slist.txt" ]; then
	echo "Error: slist.txt not found."
	exit 1
fi

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
	case $1 in
	--api-key=*)
		api_key="${1#*=}"
		shift
		;;
	*)
		echo "Unknown option: $1"
		exit 1
		;;
	esac
done

# Check if API key is provided
if [ -z "$api_key" ]; then
	echo "Error: Please provide the API key using --api-key=..."
	exit 1
fi

# Read each line from the file
while IFS= read -r line; do

	# Remove exclamation mark from the line
	cleaned_line=$(echo "$line" | tr -d '!')

	# Replace whitespace with underscores for the filename
	filename=$(echo "$cleaned_line" | tr ' ' '_').mp3

	# Construct the curl command with the line as text

	#curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/jBpfuIE2acCO8z3wKNLl" \
	#curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/nPijfmaNgvm5OSN4xM8H" \
	#curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/0m2tDjDewtOfXrhxqgrJ" \
	#curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/flHkNRp1BlvT73UL6gyz" \
	# curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/15UZGOW00QFLWYhXdrdt" \
	# curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/eVItLK1UvXctxuaRV2Oq" \
	curl -X POST "https://api.elevenlabs.io/v1/text-to-speech/FF7KdobWPaiR0vkcALHF" \
		-H "Content-Type: application/json" \
		-H "xi-api-key: $api_key" \
		-d "{\"text\": \"$line\", \"voice_settings\": {\"stability\": 0.90, \"similarity_boost\": 0.75}}" \
		--output "$filename"
	ffmpeg -i "$filename" -b:a 64k -c:a libmp3lame "smaller_$filename"
	mv "$filename" "original_$filename"
	mv "smaller_$filename" "$filename"

	sox "$filename" "robot_$filename" overdrive 10 echo 0.8 0.8 5 0.7 echo 0.8 0.7 6 0.7 echo 0.8 0.7 10 0.7 echo 0.8 0.7 12 0.7 echo 0.8 0.88 12 0.7 echo 0.8 0.88 30 0.7 echo 0.6 0.6 60 0.7 reverb
	sox "$filename" "reverb_$filename" speed 1.0
	# sox -m -v 0.4 "robot_$filename" -v 0.6 "reverb_$filename" "final_$filename";
	sox -m -v 0.4 "robot_$filename" -v 0.8 "$filename" "final_$filename"
	rm "robot_$filename"
	rm "reverb_$filename"
	mv "final_$filename" "$filename"
done <slist.txt
