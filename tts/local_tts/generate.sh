#!/bin/sh
rm *.mp3
rm *.wav

# pico2wave
while IFS= read -r line; do pico2wave -l en-US -w temp.wav "$line" && sox temp.wav -c 1 -r 16000 -b 16 "${line// /_}.wav" pitch -100; done < slist.txt

# espeak
#while IFS= read -r line; do espeak "$line" -w "${line// /_}.wav"; done < slist.txt

for file in *.wav; do sox "$file" "robot_$file" overdrive 10 echo 0.8 0.8 5 0.7 echo 0.8 0.7 6 0.7 echo 0.8 0.7 10 0.7 echo 0.8 0.7 12 0.7 echo 0.8 0.88 12 0.7 echo 0.8 0.88 30 0.7 echo 0.6 0.6 60 0.7 reverb; done
# for file in *.wav; do sox "$file" "reverb_$file" overdrive 10 reverb; done
for file in *.wav; do sox "$file" "reverb_$file" speed 1.0; done
while read -r line; do sox -m -v 1.0 "robot_${line// /_}.wav" -v 1.5 "reverb_${line// /_}.wav" "final_${line// /_}.wav"; done < slist.txt
find . -maxdepth 1 -type f -name "*.wav" ! -name "final_*.wav" -exec rm {} \;
for file in *.wav; do ffmpeg -i "$file" "${file%.wav}.mp3" && rm "$file"; done
for file in final_*.mp3; do mv "$file" "${file#final_}"; done
