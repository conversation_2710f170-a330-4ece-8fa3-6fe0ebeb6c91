#!/bin/bash

# Check if the argument is passed
if [ -z "$1" ]; then
  echo "Please provide an environment."
  exit 1
fi

# Set environment_name variable
environment_name=$1

# Check if the environment file exists
if [ -f "./envs/env.$environment_name.gd" ]; then
  # Create the softlink
  cp "./envs/env.$environment_name.gd" "./app/env/Environment.gd"
  echo "Environment changed to $environment_name."
else
  # Throw an error
  echo "$environment_name does not exist."
  exit 1
fi
